import { Context, Markup } from "telegraf";
import {
  formatOrderForDisplay,
  getUserOrdersByTgId,
  completePurchaseByBot,
} from "../firebase-service";
import { setUserSession } from "../services/session";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
  createOrderActionsKeyboard,
  createOrderBackKeyboard,
  createOrderCompletionKeyboard,
  createSupportKeyboard,
} from "../utils/keyboards";

import { handleGiftToRelayer } from "../middleware/business-connection";
import { log } from "../utils/logger";
import { MOCK_ORDER_GIFT } from "../app.constants";

const isGiftSimulation = () => process.env.GIFT_SIMULATION === "true";

export const handleOrderHelpCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    `📋 Order Help

If you need help with your order:

1. Open the marketplace using the menu button
2. Go to your profile/orders section
3. Find your order and follow the instructions
4. Contact support if you encounter issues

For immediate assistance, you can also contact our support team.`,
    createSupportKeyboard()
  );
};

export const handleContactSupportCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    `📞 Contact Support

You can reach our support team through:

• Telegram: https://t.me/prem_support_official
• Live chat in the web app

We typically respond within 24 hours.`,
    createMarketplaceInlineKeyboard()
  );
};

export const handleOpenMarketplaceCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    `🌐 Opening Marketplace

Click the button below to open the full marketplace experience:`,
    createMarketplaceInlineKeyboard()
  );
};

export const handleOrderSelectionCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];
    const tgId = ctx.from?.id?.toString();

    if (!tgId || !orderId) {
      ctx.reply(
        "❌ Unable to identify your Telegram ID or order. Please try again."
      );
      return;
    }

    // Get user orders to find the selected order
    const ordersResponse = await getUserOrdersByTgId(tgId);
    const order = ordersResponse.orders.find((o) => o.id === orderId);

    if (!order) {
      ctx.reply("❌ Order not found. It may have been completed or cancelled.");
      return;
    }

    // Check if user is buyer or seller for this order
    const isBuyer = order.buyerId === ordersResponse.userId;
    const isSeller = order.sellerId === ordersResponse.userId;

    let message = `📦 Order #${order.number}\n\n`;

    // Development mode logic
    if (isGiftSimulation()) {
      if (isBuyer && order.status === "gift_sent_to_relayer") {
        // For buyers in development mode - simulate gift retrieval
        message += `🎁 Your gift is ready for delivery!

🔧 DEV MODE: Simulating gift retrieval...`;

        try {
          await completePurchaseByBot(orderId);
          message += `\n\n✅ DEV MODE: Gift retrieved and order completed successfully!`;
        } catch (error) {
          log.error("Error completing purchase in dev mode", error, {
            operation: "dev_complete_purchase",
            orderId,
            chatId: ctx.chat?.id,
            userId: ctx.from?.id,
          });
          message += `\n\n❌ DEV MODE: Error completing purchase.`;
        }

        ctx.reply(message, createOrderBackKeyboard());
      } else if (isSeller && order.status === "paid" && order.buyerId) {
        // For sellers in development mode - simulate gift sending
        message += `🎁 This order is ready for completion!

🔧 DEV MODE: Simulating gift sending to relayer...`;

        try {
          await handleGiftToRelayer(
            ctx,
            orderId,
            MOCK_ORDER_GIFT,
            ctx.chat?.id ?? 0
          );
          message += `\n\n✅ DEV MODE: Gift sent to relayer successfully!`;
        } catch (error) {
          log.error("Error sending gift to relayer in dev mode", error, {
            operation: "dev_send_gift",
            orderId,
            chatId: ctx.chat?.id,
            userId: ctx.from?.id,
          });
          message += `\n\n❌ DEV MODE: Error sending gift to relayer.`;
        }

        ctx.reply(message, createOrderBackKeyboard());
      } else {
        // For other order statuses in dev mode
        message += `Status: ${order.status.toUpperCase()}\n\n🔧 DEV MODE: No actions available for this order status.`;
        ctx.reply(message, createOrderBackKeyboard());
      }
    } else {
      // Production mode logic
      if (isBuyer && order.status === "gift_sent_to_relayer") {
        // For buyers with gifts ready for delivery
        message += `🎁 Your gift is ready for delivery!

Please go to @premrelayer now and write in chat "Get a gift"`;

        // Save the order ID in session for the buyer
        await setUserSession(tgId, { pendingOrderId: orderId });

        ctx.reply(message, createOrderBackKeyboard());
      } else if (isSeller && order.status === "paid" && order.buyerId) {
        // For sellers with paid orders ready for completion
        message += `🎁 This order is ready for completion!

To complete this order:
1. Send the gift/item to this Prem Gift Relayer
2. The bot will verify and complete the purchase
3. Funds will be transferred to the seller

⚠️ Only send the gift when you're ready to complete the order!`;

        ctx.reply(message, createOrderActionsKeyboard(orderId));
      } else {
        // For other order statuses in production mode
        message += `Status: ${order.status.toUpperCase()}\n\n`;

        if (order.status === "active") {
          message += "This order is waiting for a buyer.";
        } else if (order.status === "paid") {
          message += "This order has been paid and is being processed.";
        } else if (order.status === "fulfilled") {
          message += "This order has been completed.";
        } else if (order.status === "cancelled") {
          message += "This order has been cancelled.";
        }

        ctx.reply(message, createOrderBackKeyboard());
      }
    }
  } catch (error) {
    log.error("Error handling order selection", error, {
      operation: "order_selection",
      chatId: ctx.chat?.id,
      userId: ctx.from?.id,
    });
    ctx.reply("❌ Failed to load order details. Please try again later.");
  }
};

export const handleBackToOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const message = "📋 Choose which orders to view:";

    const orderTypeButtons = [
      [
        Markup.button.callback("🛒 Buy Orders", "view_buy_orders"),
        Markup.button.callback("💰 Sell Orders", "view_sell_orders"),
      ],
      [Markup.button.callback("🌐 Open Marketplace", "open_marketplace")],
    ];

    ctx.editMessageText(message, Markup.inlineKeyboard(orderTypeButtons));
  } catch (error) {
    log.error("Error showing order options", error, {
      operation: "show_order_options",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(
      "❌ Failed to show order options. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleOrderCompletionCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];

    if (!orderId) {
      ctx.reply("❌ Unable to identify order. Please try again.");
      return;
    }

    ctx.reply(
      `🎁 Ready to Complete Order

Please send the gift to this @premrelayer now. You can send:`,
      createOrderCompletionKeyboard(orderId)
    );

    // Store the order ID for the next message from this user
    const userId = ctx.from?.id?.toString();
    if (userId && orderId) {
      await setUserSession(userId, { pendingOrderId: orderId });
    }
  } catch (error) {
    log.error("Error handling order completion", error, {
      operation: "order_completion",
      chatId: ctx.chat?.id,
      userId: ctx.from?.id,
    });
    ctx.reply("❌ Failed to prepare order completion. Please try again later.");
  }
};

export const handleReceiveGiftCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const userId = ctx.from?.id?.toString();

    if (!userId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🎁 Preparing your gift for delivery...");

    // Here we would implement the actual gift delivery logic
    // For now, we'll just mark the order as fulfilled
    // In a real implementation, this would involve the actual gift transfer mechanism

    ctx.reply(
      `🎉 Gift received successfully!

📦 Order has been completed.
🎁 Your gift has been delivered!

Thank you for using our marketplace!`,
      createOrderBackKeyboard()
    );
  } catch (error) {
    log.error("Error receiving gift", error, {
      operation: "receive_gift",
      chatId: ctx.chat?.id,
      userId: ctx.from?.id,
    });
    ctx.reply(
      "❌ Failed to receive gift. Please try again later.",
      createOrderBackKeyboard()
    );
  }
};

export const handleViewBuyOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.buyOrdersCount === 0) {
      ctx.editMessageText(
        `📭 You don't have any buy orders yet.

Create your first buy order using the marketplace web app!`,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const buyOrders = ordersResponse.buyOrders;

    let message = `🛒 Your Buy Orders (${buyOrders.length} total)\n\n`;

    // Create inline keyboard with order buttons - show only gift ready orders for buyers
    const giftReadyOrders = buyOrders.filter(
      (order) => order.status === "gift_sent_to_relayer"
    );
    const orderButtons = giftReadyOrders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    // Add navigation buttons
    orderButtons.push([
      Markup.button.callback("🔙 Back to Order Types", "back_to_orders"),
    ]);
    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (buyOrders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    log.error("Error fetching user buy orders", error, {
      operation: "fetch_buy_orders",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(
      "❌ Failed to fetch your buy orders. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleViewSellOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.sellOrdersCount === 0) {
      ctx.editMessageText(
        `📭 You don't have any sell orders yet.

Create your first sell order using the marketplace web app!`,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const sellOrders = ordersResponse.sellOrders;

    let message = `💰 Your Sell Orders (${sellOrders.length} total)\n\n`;

    const orderButtons = sellOrders
      .slice(0, 10)
      .filter((order) => order.status === "paid")
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🔙 Back to Order Types", "back_to_orders"),
    ]);
    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (sellOrders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    log.error("Error fetching user sell orders", error, {
      operation: "fetch_sell_orders",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(
      "❌ Failed to fetch your sell orders. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleBackToMenuCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    `🔙 Back to Menu

You can use the buttons below to navigate or open the marketplace:`,
    createMainKeyboard()
  );
};
