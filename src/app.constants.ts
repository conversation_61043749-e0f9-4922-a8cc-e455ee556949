import { OrderGift } from "@mikerudenko/marketplace-shared";

export const CDN_URL = "https://cdn.changes.tg/gifts";

export const BOT_TOKEN = process.env.BOT_TOKEN;

export const WEB_APP_URL = process.env.WEB_APP_URL as string;

export const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;
export const FIREBASE_REGION = process.env.FIREBASE_REGION ?? "europe-central2";

export const PORT = process.env.PORT ?? 8080;
export const NODE_ENV = process.env.NODE_ENV ?? "development";
export const WEBHOOK_URL = process.env.WEBHOOK_URL;

export type TelegramGift = {
  gift: {
    base_name: string;
    name: string;
    number: number;
    model: {
      name: string;
      sticker: Sticker;
      rarity_per_mille: number;
    };
    symbol: {
      name: string;
      sticker: Sticker & { needs_repainting?: boolean };
      rarity_per_mille: number;
    };
    backdrop: {
      name: string;
      colors: {
        center_color: number;
        edge_color: number;
        symbol_color: number;
        text_color: number;
      };
      rarity_per_mille: number;
    };
  };
  owned_gift_id: string;
  transfer_star_count: number;
  origin: string;
};

type Sticker = {
  width: number;
  height: number;
  emoji: string;
  is_animated: boolean;
  is_video: boolean;
  type: "custom_emoji";
  custom_emoji_id: string;
  file_id: string;
  file_unique_id: string;
  file_size: number;
  thumbnail: Thumbnail;
  thumb: Thumbnail;
};

type Thumbnail = {
  file_id: string;
  file_unique_id: string;
  file_size: number;
  width: number;
  height: number;
};

export type TelegramBusinessMessageContext = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "channel" | "supergroup" | "group";
    };
    date: number;
    unique_gift: TelegramGift;
  };
};

export const MOCK_ORDER_GIFT: OrderGift = {
  base_name: "Lol Pop",
  owned_gift_id: "155",
  backdrop: {
    name: "Mint Green",
    colors: {
      center_color: 8309634,
      edge_color: 4562522,
      symbol_color: 158498,
      text_color: 12451788,
    },
    rarity_per_mille: 15,
  },
  model: {
    name: "Tsunami",
    rarity_per_mille: 12,
  },
  symbol: {
    name: "Money Bag",
    rarity_per_mille: 4,
  },
};
