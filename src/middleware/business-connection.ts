import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { completePurchaseByBot, sendGiftToRelayer } from "../firebase-service";
import { clearUserSession, getUserSession } from "../services/session";
import {
  getBusinessConnectionId,
  getOrderById,
  getGiftToTransfer,
  getUniqueGift,
  transferGift,
} from "../utils/business-connection-helpers";
import { validateSentGiftWithOrder } from "../utils/gift-validation";
import { log } from "../utils/logger";
import { TelegramBusinessMessageContext } from "../app.constants";
import { OrderGift } from "@mikerudenko/marketplace-shared";

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    log.info("Business connection middleware started", {
      operation: "business_connection_middleware_start",
    });

    const update = ctx.update as unknown as TelegramBusinessMessageContext;

    console.log("ctx.update", JSON.stringify(ctx.update));

    if (!update?.business_message) {
      log.info("No business message found, skipping middleware", {
        operation: "business_connection_middleware",
      });
      await next();
      return;
    }

    const chat_id = update.business_message.chat.id;
    log.info("Processing business message", {
      operation: "business_connection_middleware",
      chat_id,
    });

    const userId = update.business_message.from?.id?.toString();
    if (!userId) {
      log.warn("No user ID found in business message", {
        operation: "business_connection_middleware",
        chat_id,
      });
      await next();
      return;
    }

    log.info("User ID extracted from business message", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
    });

    const session = await getUserSession(userId);
    log.info("User session retrieved", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      hasSession: !!session,
      pendingOrderId: session?.pendingOrderId,
    });

    const pendingOrderId = session?.pendingOrderId;

    if (!pendingOrderId) {
      log.warn("No pending order found for user", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
      });
      await ctx.telegram.sendMessage(chat_id, "No pending order found");
      await next();
      return;
    }

    const giftToTransfer = getGiftToTransfer(ctx);
    log.info("Gift ID extraction result", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      pendingOrderId,
      giftIdToTransfer: giftToTransfer,
    });

    if (giftToTransfer) {
      log.info("Processing gift transfer to relayer", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
        giftIdToTransfer: giftToTransfer,
      });

      // Get order with firestore by pendingOrderId, check if collectionId matches
      const order = await getOrderById(pendingOrderId);
      log.info("Order retrieval result", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
        orderFound: !!order,
        collectionId: order?.collectionId,
      });

      if (!order) {
        log.warn("Order not found for pending order ID", {
          operation: "business_connection_middleware",
          chat_id,
          userId,
          pendingOrderId,
        });
        await ctx.telegram.sendMessage(
          chat_id,
          MESSAGES.BUSINESS_CONNECTION.ORDER_NOT_FOUND
        );
        await next();
        return;
      }

      // Validate the sent gift with the order
      try {
        const uniqueGift = getUniqueGift(ctx);
        log.info("Starting gift validation", {
          operation: "gift_validation",
          chat_id,
          userId,
          pendingOrderId,
          collectionId: order.collectionId,
          uniqueGift,
        });

        const isValid = await validateSentGiftWithOrder(
          order.collectionId,
          uniqueGift
        );

        log.info("Gift validation completed", {
          operation: "gift_validation",
          chat_id,
          userId,
          pendingOrderId,
          collectionId: order.collectionId,
          uniqueGift,
          isValid,
        });

        if (!isValid) {
          log.warn("Gift validation failed - incorrect gift", {
            operation: "gift_validation",
            chat_id,
            userId,
            pendingOrderId,
            collectionId: order.collectionId,
            uniqueGift,
          });
          await ctx.telegram.sendMessage(
            chat_id,
            MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
          );
          await next();
          return;
        }
      } catch (error) {
        log.error("Gift validation failed with exception", error, {
          operation: "gift_validation",
          chat_id,
          userId,
          pendingOrderId,
        });
        await ctx.telegram.sendMessage(
          chat_id,
          MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
        );
        await next();
        return;
      }

      log.info("Gift validation successful, processing gift to relayer", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
        giftToTransfer,
      });

      // Gift is correct, process it
      await handleGiftToRelayer(ctx, pendingOrderId, giftToTransfer, chat_id);

      // Send notification to buyer when gift is sent to relayer
      try {
        if (order.buyer_tg_id) {
          await ctx.telegram.sendMessage(
            parseInt(order.buyer_tg_id),
            MESSAGES.BUSINESS_CONNECTION.GIFT_READY_FOR_BUYER(order.number)
          );
          log.info("Buyer notification sent successfully", {
            operation: "business_connection_middleware",
            orderId: pendingOrderId,
            buyerTgId: order.buyer_tg_id,
          });
        }
      } catch (error) {
        log.error("Failed to send buyer notification", error, {
          operation: "business_connection_middleware",
          orderId: pendingOrderId,
          buyerTgId: order.buyer_tg_id,
        });
      }

      // Clear user session after successful processing
      await clearUserSession(userId);
      log.info("User session cleared after successful gift processing", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
      });
      await next();
      return;
    }

    log.info("No gift to transfer, checking for buyer gift request", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      pendingOrderId,
    });

    // Logic for buyer to request the gift
    // If no gift to transfer, check if user has an order with status 'gift_sent_to_relayer'
    const existingOrder = await getOrderById(pendingOrderId);
    log.info("Existing order retrieval for buyer request", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      pendingOrderId,
      orderFound: !!existingOrder,
      buyerTgId: existingOrder?.buyer_tg_id,
      gift: existingOrder?.gift,
    });

    if (!existingOrder) {
      log.warn("No existing order found for buyer request", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.NO_ORDER_FOR_PROCESSING
      );
      await next();
      return;
    }

    // Validate that current user is the original buyer
    if (existingOrder.buyer_tg_id !== userId) {
      log.warn("User not authorized - not original buyer", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
        originalBuyerId: existingOrder.buyer_tg_id,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        "You are not authorized to request this gift. Only the original buyer can claim the gift."
      );
      await next();
      return;
    }

    const giftToTransferToBuyer = existingOrder.gift.owned_gift_id;

    if (!giftToTransferToBuyer) {
      log.warn("No gift available to transfer to buyer", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.NO_GIFT_TO_TRANSFER
      );
      await next();
      return;
    }

    const businessConnectionId = getBusinessConnectionId(ctx);
    log.info("Business connection ID retrieved", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      pendingOrderId,
      businessConnectionId,
      giftToTransferToBuyer,
    });

    if (businessConnectionId) {
      log.info("Starting gift transfer to buyer", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
        businessConnectionId,
        giftToTransferToBuyer,
      });

      await transferGift(
        ctx,
        businessConnectionId,
        chat_id,
        giftToTransferToBuyer
      );
      await completePurchaseByBot(pendingOrderId);
      await clearUserSession(userId);

      log.info("Gift transfer to buyer completed successfully", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
      });
    } else {
      log.error("Missing business connection ID for gift transfer", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_MISSING_INFO
      );
    }

    log.info("Business connection middleware completed", {
      operation: "business_connection_middleware_end",
      chat_id,
      userId,
      pendingOrderId,
    });

    await next();
  } catch (error) {
    log.error("Error handling update", error, {
      operation: "business_connection_middleware",
    });
    await next();
  }
};

export const handleGiftToRelayer = async (
  ctx: Context,
  orderId: string,
  gift: OrderGift,
  chat_id: number
) => {
  try {
    log.info("Starting gift to relayer processing", {
      operation: "handle_gift_to_relayer",
      orderId,
      gift,
      chat_id,
    });

    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.PROCESSING_GIFT
    );

    // Update order status to gift_sent_to_relayer
    const result = await sendGiftToRelayer(orderId, gift);

    log.info("Send gift to relayer result", {
      operation: "handle_gift_to_relayer",
      orderId,
      gift,
      chat_id,
      success: result.success,
    });

    if (result.success) {
      log.info("Gift successfully sent to relayer", {
        operation: "handle_gift_to_relayer",
        orderId,
        gift,
        chat_id,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_SENT_SUCCESS
      );
    } else {
      log.error("Failed to send gift to relayer", {
        operation: "handle_gift_to_relayer",
        orderId,
        gift,
        chat_id,
        result,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_PROCESSING_GENERIC_ERROR(orderId)
      );
    }
  } catch (error) {
    log.error("Error handling gift to relayer", error, {
      operation: "handle_gift_to_relayer",
      orderId,
      gift,
      chat_id,
    });

    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.GIFT_PROCESSING_GENERIC_ERROR(orderId)
    );
  }
};
