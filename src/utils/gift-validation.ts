import { CDN_URL, TelegramGift } from "../app.constants";
import { log } from "./logger";

interface GiftAttribute {
  name: string;
  rarityPermille: number;
}

const fetchIdToNameMapping = async (): Promise<Record<
  string,
  string
> | null> => {
  try {
    const response = await fetch(`${CDN_URL}/id-to-name.json`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as Record<string, string>;
  } catch (error) {
    log.error("Error fetching id-to-name mapping from CDN", error, {
      operation: "gift_validation",
      component: "fetch_id_to_name_mapping",
    });
    return null;
  }
};

const fetchAttributeData = async (
  collectionName: string,
  attributeType: "models" | "patterns" | "backdrops"
): Promise<GiftAttribute[] | null> => {
  try {
    const response = await fetch(
      `${CDN_URL}/${attributeType}/${collectionName}/${attributeType}.json`
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as GiftAttribute[];
  } catch (error) {
    log.error(
      `Error fetching ${attributeType} data for collection ${collectionName} from CDN`,
      error,
      {
        operation: "gift_validation",
        component: "fetch_attribute_data",
        attributeType,
        collectionName,
      }
    );
    return null;
  }
};

const validateAttribute = (
  giftAttributeName: string,
  giftRarityPermille: number,
  validAttributes: GiftAttribute[],
  attributeType: string
): boolean => {
  const matchingAttribute = validAttributes.find(
    (attr) =>
      attr.name === giftAttributeName &&
      attr.rarityPermille === giftRarityPermille
  );

  if (!matchingAttribute) {
    log.error(
      `Invalid ${attributeType}: ${giftAttributeName} with rarity ${giftRarityPermille} not found`,
      undefined,
      {
        operation: "gift_validation",
        component: "validate_attribute",
        attributeType,
        giftAttributeName,
        giftRarityPermille,
      }
    );
    return false;
  }

  return true;
};

export const validateSentGiftWithOrder = async (
  orderCollectionId: string,
  uniqueGift: TelegramGift
): Promise<boolean> => {
  try {
    // Step 1: Get collection name by orderCollectionId
    const idToNameMapping = await fetchIdToNameMapping();

    if (!idToNameMapping) {
      log.warn(
        "Failed to fetch collection mapping from CDN, skipping gift validation",
        {
          operation: "gift_validation",
          component: "validate_sent_gift_with_order",
          orderCollectionId,
        }
      );
      return true; // Skip validation if CDN request failed
    }

    const collectionName = idToNameMapping[orderCollectionId];

    if (!collectionName) {
      log.error(
        `Collection name not found for ID: ${orderCollectionId}`,
        undefined,
        {
          operation: "gift_validation",
          component: "validate_sent_gift_with_order",
          orderCollectionId,
        }
      );
      throw new Error("Invalid gift send");
    }

    // Step 2: Validate model
    const modelsData = await fetchAttributeData(collectionName, "models");
    if (!modelsData) {
      log.warn(
        "Failed to fetch models data from CDN, skipping gift validation",
        {
          operation: "gift_validation",
          component: "validate_sent_gift_with_order",
          collectionName,
        }
      );
      return true; // Skip validation if CDN request failed
    }

    const isModelValid = validateAttribute(
      uniqueGift.gift.model.name,
      uniqueGift.gift.model.rarity_per_mille,
      modelsData,
      "model"
    );

    if (!isModelValid) {
      throw new Error("Invalid gift send");
    }

    // Step 3: Validate symbol (patterns)
    const patternsData = await fetchAttributeData(collectionName, "patterns");
    if (!patternsData) {
      log.warn(
        "Failed to fetch patterns data from CDN, skipping gift validation",
        {
          operation: "gift_validation",
          component: "validate_sent_gift_with_order",
          collectionName,
        }
      );
      return true; // Skip validation if CDN request failed
    }

    const isSymbolValid = validateAttribute(
      uniqueGift.gift.symbol.name,
      uniqueGift.gift.symbol.rarity_per_mille,
      patternsData,
      "symbol"
    );

    if (!isSymbolValid) {
      throw new Error("Invalid gift send");
    }

    // Step 4: Validate backdrops
    const backdropsData = await fetchAttributeData(collectionName, "backdrops");
    if (!backdropsData) {
      log.warn(
        "Failed to fetch backdrops data from CDN, skipping gift validation",
        {
          operation: "gift_validation",
          component: "validate_sent_gift_with_order",
          collectionName,
        }
      );
      return true; // Skip validation if CDN request failed
    }

    const isBackdropValid = validateAttribute(
      uniqueGift.gift.backdrop.name,
      uniqueGift.gift.backdrop.rarity_per_mille,
      backdropsData,
      "backdrop"
    );

    if (!isBackdropValid) {
      throw new Error("Invalid gift send");
    }

    log.info("Backdrop is valid", {
      operation: "gift_validation",
      component: "validate_sent_gift_with_order",
      collectionName,
    });

    // All validations passed
    return true;
  } catch (error) {
    log.error("Gift validation failed", error, {
      operation: "gift_validation",
      component: "validate_sent_gift_with_order",
      orderCollectionId,
    });
    throw error;
  }
};
