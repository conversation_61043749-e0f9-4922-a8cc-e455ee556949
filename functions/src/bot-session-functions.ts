import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { validateBotRequest } from "./services/bot-validation-service";
import { commonFunctionsConfig } from "./constants";
import { BotSessionEntity } from "@mikerudenko/marketplace-shared";
import { BotSessionLogger } from "./logger/bot-session-logger";

const db = admin.firestore();
const botSessionLogger = new BotSessionLogger();

export const saveUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
  sessionData: {
    pendingOrderId?: string;
    echoMode?: boolean;
  };
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken, sessionData } = request.data;

  validateBotRequest({ userId, botToken });

  try {
    const sessionDoc = db.collection("bot_sessions").doc(userId);
    const existingSession = await sessionDoc.get();

    const sessionEntity: Partial<BotSessionEntity> = {
      id: userId,
      pendingOrderId: sessionData.pendingOrderId,
      echoMode: sessionData.echoMode,
      createdAt: existingSession.exists
        ? existingSession.data()?.createdAt
        : (admin.firestore.FieldValue.serverTimestamp() as any),
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
    };

    await sessionDoc.set(sessionEntity, { merge: true });

    botSessionLogger.logSessionSaved(userId, sessionData);

    return {
      success: true,
      message: "Session saved successfully",
    };
  } catch (error) {
    botSessionLogger.logSessionSaveError(error, userId);
    throw new HttpsError("internal", "Failed to save session");
  }
});

export const getUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken } = request.data;

  validateBotRequest({ userId, botToken });

  try {
    const sessionDoc = await db.collection("bot_sessions").doc(userId).get();

    if (!sessionDoc.exists) {
      botSessionLogger.logSessionNotFound(userId);
      return {
        success: true,
        session: null,
      };
    }

    const sessionData = sessionDoc.data() as BotSessionEntity;

    botSessionLogger.logSessionRetrieved(userId);

    return {
      success: true,
      session: {
        pendingOrderId: sessionData.pendingOrderId,
        echoMode: sessionData.echoMode,
      },
    };
  } catch (error) {
    botSessionLogger.logSessionRetrieveError(error, userId);
    throw new HttpsError("internal", "Failed to retrieve session");
  }
});

export const clearUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken } = request.data;

  validateBotRequest({ userId, botToken });

  try {
    const sessionDoc = db.collection("bot_sessions").doc(userId);
    await sessionDoc.delete();

    botSessionLogger.logSessionCleared(userId);

    return {
      success: true,
      message: "Session cleared successfully",
    };
  } catch (error) {
    botSessionLogger.logSessionClearError(error, userId);
    throw new HttpsError("internal", "Failed to clear session");
  }
});
