import { BaseLogger } from "./logger";

export interface TelegramValidationDTO {
  isValid: boolean;
  error?: string;
  hasUser: boolean;
}

export interface TelegramUserCreatedDTO {
  userId: string;
  telegramId: string;
}

export interface TelegramAuthErrorDTO {
  error: unknown;
  requestData: any;
}

export class Telegram<PERSON>uth<PERSON>ogger extends BaseLogger {
  constructor() {
    super("telegram-auth");
  }

  logAuthStarted(): void {
    this.info("Starting Telegram authentication", "telegram_signin", {});
  }

  logValidationResult(data: TelegramValidationDTO): void {
    this.info("Telegram data validation result", "telegram_auth", {
      isValid: data.isValid,
      error: data.error,
      hasUser: data.hasUser,
    });
  }

  logUserCreated(data: TelegramUserCreatedDTO): void {
    this.info(`New Telegram user created: ${data.userId} (TG ID: ${data.telegramId})`, "telegram_signin", {
      userId: data.userId,
      telegramId: data.telegramId,
      action: "user_created",
    });
  }

  logAuthError(data: TelegramAuthErrorDTO): void {
    this.error("Telegram authentication error", data.error, "telegram_signin", {
      requestData: data.requestData,
    });
  }
}
