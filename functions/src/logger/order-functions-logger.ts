import { BaseLogger } from "./logger";

export interface OrderErrorDTO {
  error: unknown;
  operation: string;
  requestData?: any;
  userId?: string;
  orderId?: string;
  buyerId?: string;
  sellerId?: string;
}

export interface OrderDebugDTO {
  orderId: string;
  userId: string;
  environment?: string;
}

export interface OrderFulfilledDTO {
  originalOrderId: string;
  newOrderId: string;
  userId: string;
  price: number;
}

// Buyer Order Logger
export class BuyerOrderLogger extends BaseLogger {
  constructor() {
    super("buyer-order");
  }

  logPurchaseError(data: OrderErrorDTO): void {
    this.error("Error making purchase as buyer", data.error, "buyer_purchase", {
      buyerId: data.buyerId,
      orderId: data.orderId,
    });
  }
}

// Seller Order Logger
export class SellerOrderLogger extends BaseLogger {
  constructor() {
    super("seller-order");
  }

  logPurchaseError(data: OrderErrorDTO): void {
    this.error(
      "Error making purchase as seller",
      data.error,
      "purchase_as_seller",
      {
        requestData: data.requestData,
        userId: data.userId,
      }
    );
  }
}

// General Order Logger
export class GeneralOrderLogger extends BaseLogger {
  constructor() {
    super("general-order");
  }

  logCancelOrderRequest(data: OrderDebugDTO): void {
    this.debug("Cancel order request received", "cancel_order", {
      orderId: data.orderId,
      userId: data.userId,
      environment: data.environment,
    });
  }
}

// Bot Order Logger
export class BotOrderLogger extends BaseLogger {
  constructor() {
    super("bot-order");
  }

  logGetOrderError(data: { error: unknown; orderId: string }): void {
    this.error("Error getting order by ID", data.error, "get_order_by_id", {
      orderId: data.orderId,
    });
  }

  logGetUserOrdersError(data: { error: unknown; userId?: string }): void {
    this.error(
      "Error getting user orders",
      data.error,
      "get_user_orders_by_bot",
      {
        userId: data.userId,
      }
    );
  }

  logSendGiftToRelayerError(data: { error: unknown; orderId: string }): void {
    this.error(
      "Error sending gift to relayer",
      data.error,
      "send_gift_to_relayer_by_bot",
      {
        orderId: data.orderId,
      }
    );
  }

  logCompletePurchaseError(data: { error: unknown; orderId: string }): void {
    this.error(
      "Error completing purchase",
      data.error,
      "complete_purchase_by_bot",
      {
        orderId: data.orderId,
      }
    );
  }
}

// Fulfill and Resell Logger
export class FulfillAndResellLogger extends BaseLogger {
  constructor() {
    super("fulfill-and-resell");
  }

  logOrderFulfilledAndResellCreated(data: OrderFulfilledDTO): void {
    this.info(
      "Order fulfilled and resell order created",
      "fulfill_and_resell",
      {
        originalOrderId: data.originalOrderId,
        newOrderId: data.newOrderId,
        userId: data.userId,
        price: data.price,
      }
    );
  }

  logFulfillAndResellError(data: OrderErrorDTO): void {
    this.error(
      "Error fulfilling order and creating resell order",
      data.error,
      "fulfill_and_resell",
      {
        requestData: data.requestData,
        userId: data.userId,
      }
    );
  }
}

// Transaction History Logger
export class TransactionHistoryLogger extends BaseLogger {
  constructor() {
    super("transaction-history");
  }

  logTransactionRecordCreated(data: {
    userId: string;
    txType: string;
    originalAmount: number;
    signedAmount: number;
    orderId?: string;
  }): void {
    this.info(
      "Transaction history record created",
      "create_transaction_record",
      {
        userId: data.userId,
        txType: data.txType,
        originalAmount: data.originalAmount,
        signedAmount: data.signedAmount,
        orderId: data.orderId,
      }
    );
  }
}

// Fee Service Logger
export class FeeServiceLogger extends BaseLogger {
  constructor() {
    super("fee-service");
  }

  logAppConfigNotFound(): void {
    this.warn("App config not found, using zero fees", "app_config_fetch", {});
  }
}
