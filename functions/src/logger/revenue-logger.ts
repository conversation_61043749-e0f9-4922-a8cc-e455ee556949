import { BaseLogger, LogContext } from "./logger";

export class RevenueLogger extends BaseLogger {
  constructor() {
    super("revenue");
  }

  logRevenueWithdrawal(
    withdrawAmount: number,
    johnDowWallet: string,
    userId: string,
    transactionHash?: string
  ): void {
    this.info(
      `Revenue transferred: ${withdrawAmount.toFixed(4)} TON sent to <PERSON> wallet`,
      "revenue_withdrawal",
      {
        withdrawAmount,
        johnDowWallet,
        userId,
        transactionHash,
      }
    );
  }

  logRevenueWithdrawalError(
    error: Error | any,
    withdrawAmount: number,
    johnDowWallet: string,
    userId?: string
  ): void {
    this.error("Error in withdrawRevenue function", error, "revenue_withdrawal", {
      withdrawAmount,
      johnDowWallet,
      userId,
    });
  }
}
