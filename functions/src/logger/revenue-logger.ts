import { BaseLogger } from "./base-logger";

export interface RevenueWithdrawalDTO {
  withdrawAmount: number;
  johnDowWallet: string;
  userId: string;
  transactionHash?: string;
}

export interface RevenueWithdrawalErrorDTO {
  error: unknown;
  withdrawAmount: number;
  johnDowWallet: string;
  userId?: string;
}

export class RevenueLogger extends BaseLogger {
  constructor() {
    super("revenue");
  }

  logRevenueWithdrawal(data: RevenueWithdrawalDTO): void {
    this.info(
      `Revenue transferred: ${data.withdrawAmount.toFixed(
        4
      )} TON sent to <PERSON> wallet`,
      "revenue_withdrawal",
      {
        withdrawAmount: data.withdrawAmount,
        johnDowWallet: data.johnDowWallet,
        userId: data.userId,
        transactionHash: data.transactionHash,
      }
    );
  }

  logRevenueWithdrawalError(data: RevenueWithdrawalErrorDTO): void {
    this.error(
      "Error in withdrawRevenue function",
      data.error,
      "revenue_withdrawal",
      {
        withdrawAmount: data.withdrawAmount,
        johnDowWallet: data.johnDowWallet,
        userId: data.userId,
      }
    );
  }
}
