import { BaseLogger } from "./logger";

export interface ReferrerPointsUpdateDTO {
  referrerId: string;
  referralCount: number;
  previousPoints: number;
  newPoints: number;
}

export interface ReferrerNotFoundDTO {
  referrerId: string;
}

export interface ReferrerPointsErrorDTO {
  error: unknown;
  referrerId: string;
  userId: string;
}

export interface ReferrerSkipUpdateDTO {
  userId: string;
  existingReferrerId: string;
  attemptedReferrerId: string;
}

export interface UserProfileUpdateDTO {
  userId: string;
  updatedFields: string[];
  preparedData: any;
}

export interface UserProfileErrorDTO {
  error: unknown;
  userId: string;
  requestData: any;
}

export class UserProfileLogger extends BaseLogger {
  constructor() {
    super("user-profile");
  }

  logReferrerPointsUpdated(data: ReferrerPointsUpdateDTO): void {
    this.info("Updated referrer points", "referrer_points_update", {
      referrer_id: data.referrerId,
      referrer_user_id: data.referrerId,
      referral_count: data.referralCount,
      previous_points: data.previousPoints,
      new_points: data.newPoints,
    });
  }

  logReferrerNotFound(data: ReferrerNotFoundDTO): void {
    this.warn(`Referrer with user ID ${data.referrerId} not found`, "referrer_points_update", {
      referrer_id: data.referrerId,
      action: "referrer_not_found",
    });
  }

  logReferrerPointsError(data: ReferrerPointsErrorDTO): void {
    this.error("Error updating referrer points", data.error, "referrer_points_update", {
      referrer_id: data.referrerId,
      userId: data.userId,
    });
  }

  logReferrerUpdateSkipped(data: ReferrerSkipUpdateDTO): void {
    this.info(
      `User ${data.userId} already has referrer_id: ${data.existingReferrerId}, not updating`,
      "user_profile_update",
      {
        userId: data.userId,
        existing_referrer_id: data.existingReferrerId,
        attempted_referrer_id: data.attemptedReferrerId,
        action: "skip_referrer_update",
      }
    );
  }

  logProfileUpdated(data: UserProfileUpdateDTO): void {
    this.info(`User profile updated for ${data.userId}`, "user_profile_update", {
      userId: data.userId,
      updatedFields: data.updatedFields,
      preparedData: data.preparedData,
    });
  }

  logProfileUpdateError(data: UserProfileErrorDTO): void {
    this.error("Error in changeUserData function", data.error, "user_profile_update", {
      userId: data.userId,
      requestData: data.requestData,
    });
  }
}
