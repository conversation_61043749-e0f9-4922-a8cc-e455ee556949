import { BaseLogger } from "./base-logger";

export interface TransactionProcessingDTO {
  transactionId: string;
  sender: string;
  amount: number;
}

export interface TransactionWarningDTO {
  walletAddress: string;
  transactionId: string;
}

export interface TransactionSuccessDTO {
  transactionId: string;
  userId: string;
  userTgId?: string;
  amount: number;
}

export class TonMonitorLogger extends BaseLogger {
  constructor() {
    super("ton-monitor");
  }

  logTransactionProcessing(data: TransactionProcessingDTO): void {
    this.info("Processing transaction", "transaction_processing", {
      transactionId: data.transactionId,
      sender: data.sender,
      amount: data.amount,
    });
  }

  logUserNotFound(data: TransactionWarningDTO): void {
    this.warn("No user found for wallet address", "transaction_processing", {
      walletAddress: data.walletAddress,
      transactionId: data.transactionId,
    });
  }

  logTopupProcessed(data: TransactionSuccessDTO): void {
    this.info("Successfully processed topup", "transaction_processing", {
      transactionId: data.transactionId,
      userId: data.userId,
      userTgId: data.userTgId,
      amount: data.amount,
    });
  }
}
