import { BaseLogger } from "./base-logger";

export interface WithdrawalLimitCheckDTO {
  userId: string;
  requestedAmount: number;
  maxWithdrawalAmount: number;
  currentWithdrawn: number;
  remainingLimit: number;
  canWithdraw: boolean;
}

export interface WithdrawalLimitErrorDTO {
  error: unknown;
  userId: string;
  requestedAmount: number;
  maxWithdrawalAmount: number;
}

export interface WithdrawalTrackingUpdateDTO {
  userId: string;
  withdrawnAmount: number;
  newWithdrawnAmount: number;
  resetTime: Date;
}

export interface WithdrawalTrackingErrorDTO {
  error: unknown;
  userId: string;
  withdrawnAmount: number;
}

export class WithdrawalLimitLogger extends BaseLogger {
  constructor() {
    super("withdrawal-limit");
  }

  logLimitCheckCompleted(data: WithdrawalLimitCheckDTO): void {
    this.info("Withdrawal limit check completed", "check_withdrawal_limit", {
      userId: data.userId,
      requestedAmount: data.requestedAmount,
      maxWithdrawalAmount: data.maxWithdrawalAmount,
      currentWithdrawn: data.currentWithdrawn,
      remainingLimit: data.remainingLimit,
      canWithdraw: data.canWithdraw,
    });
  }

  logLimitCheckError(data: WithdrawalLimitErrorDTO): void {
    this.error(
      "Error checking withdrawal limit",
      data.error,
      "check_withdrawal_limit",
      {
        userId: data.userId,
        requestedAmount: data.requestedAmount,
        maxWithdrawalAmount: data.maxWithdrawalAmount,
      }
    );
  }

  logTrackingUpdated(data: WithdrawalTrackingUpdateDTO): void {
    this.info("Withdrawal tracking updated", "update_withdrawal_tracking", {
      userId: data.userId,
      withdrawnAmount: data.withdrawnAmount,
      newWithdrawnAmount: data.newWithdrawnAmount,
      resetTime: data.resetTime,
    });
  }

  logTrackingUpdateError(data: WithdrawalTrackingErrorDTO): void {
    this.error(
      "Error updating withdrawal tracking",
      data.error,
      "update_withdrawal_tracking",
      {
        userId: data.userId,
        withdrawnAmount: data.withdrawnAmount,
      }
    );
  }
}
