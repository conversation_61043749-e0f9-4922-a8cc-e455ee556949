import { log } from "../utils/logger";

export interface LogContext {
  userId?: string;
  orderId?: string;
  transactionId?: string;
  collectionId?: string;
  operation?: string;
  [key: string]: any;
}

export abstract class BaseLogger {
  protected readonly serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  protected createContext(
    operation: string,
    additionalContext?: LogContext
  ): LogContext {
    return {
      service: this.serviceName,
      operation,
      ...additionalContext,
    };
  }

  protected info(
    message: string,
    operation: string,
    context?: LogContext
  ): void {
    log.info(message, this.createContext(operation, context));
  }

  protected error(
    message: string,
    error: unknown,
    operation: string,
    context?: LogContext
  ): void {
    log.error(message, error, this.createContext(operation, context));
  }

  protected warn(
    message: string,
    operation: string,
    context?: LogContext
  ): void {
    log.warn(message, this.createContext(operation, context));
  }

  protected debug(
    message: string,
    operation: string,
    context?: LogContext
  ): void {
    log.debug(message, this.createContext(operation, context));
  }
}
