import { BaseLogger } from "./base-logger";

export interface LimitedCollectionsStartDTO {
  status: string;
}

export interface LimitedGiftsFoundDTO {
  count: number;
}

export interface LimitedCollectionsErrorDTO {
  error: unknown;
  status: string;
}

export interface MonitorTriggeredDTO {
  status: string;
  timestamp: string;
}

export class LimitedCollectionsMonitorLogger extends BaseLogger {
  constructor() {
    super("limited-collections-monitor");
  }

  logNoGiftsFound(): void {
    this.info("No gifts found in result", "fetch_limited_collections", {
      monitor: "limited_collections",
      status: "no_gifts",
    });
  }

  logLimitedGiftsFound(data: LimitedGiftsFoundDTO): void {
    this.info(
      "Found limited gifts from Telegram API",
      "fetch_limited_collections",
      {
        monitor: "limited_collections",
        count: data.count,
      }
    );
  }

  logCheckStarted(data: LimitedCollectionsStartDTO): void {
    this.info(
      "Starting limited collections check",
      "check_limited_collections",
      {
        monitor: "limited_collections",
        status: data.status,
      }
    );
  }

  logNoCollectionsFound(): void {
    this.info(
      "No limited collections found from Telegram API",
      "check_limited_collections",
      {
        monitor: "limited_collections",
        status: "no_collections",
      }
    );
  }

  logCollectionsFound(data: LimitedGiftsFoundDTO): void {
    this.info(
      "Found limited collections from Telegram API",
      "check_limited_collections",
      {
        monitor: "limited_collections",
        count: data.count,
      }
    );
  }

  logMonitorTriggered(data: MonitorTriggeredDTO): void {
    this.info("Limited collections monitor triggered", "monitor_triggered", {
      monitor: "limited_collections",
      status: data.status,
      timestamp: data.timestamp,
    });
  }

  logMonitorCompleted(): void {
    this.info(
      "Limited collections monitor completed successfully",
      "monitor_completed",
      {
        monitor: "limited_collections",
        status: "completed",
      }
    );
  }

  logMonitorFailed(data: LimitedCollectionsErrorDTO): void {
    this.error(
      "Limited collections monitor failed",
      data.error,
      "monitor_failed",
      {
        monitor: "limited_collections",
        status: data.status,
      }
    );
  }
}
