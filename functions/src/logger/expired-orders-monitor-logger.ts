import { BaseLogger } from "./logger";

export interface ExpiredOrderProcessedDTO {
  orderId: string;
  status: string;
  message: string;
}

export interface ExpiredOrderErrorDTO {
  error: unknown;
  orderId: string;
}

export interface MonitorStatusDTO {
  status: string;
  timestamp?: string;
}

export interface MonitorErrorDTO {
  error: unknown;
  status: string;
}

export class ExpiredOrdersMonitorLogger extends BaseLogger {
  constructor() {
    super("expired-orders-monitor");
  }

  logOrderProcessed(data: ExpiredOrderProcessedDTO): void {
    this.info("Successfully processed expired order", "process_expired_order", {
      monitor: "expired_orders",
      orderId: data.orderId,
      status: data.status,
      message: data.message,
    });
  }

  logOrderProcessError(data: ExpiredOrderErrorDTO): void {
    this.error("Failed to process expired order", data.error, "process_expired_order", {
      monitor: "expired_orders",
      orderId: data.orderId,
    });
  }

  logProcessingCompleted(): void {
    this.info("Expired orders processing completed", "process_expired_orders", {
      monitor: "expired_orders",
      status: "completed",
    });
  }

  logProcessingError(data: { error: unknown }): void {
    this.error("Error in processExpiredOrders", data.error, "process_expired_orders", {
      monitor: "expired_orders",
    });
  }

  logMonitorTriggered(data: MonitorStatusDTO): void {
    this.info("Expired orders monitor triggered", "monitor_triggered", {
      monitor: "expired_orders",
      status: data.status,
      timestamp: data.timestamp,
    });
  }

  logMonitorCompleted(): void {
    this.info("Expired orders monitor completed successfully", "monitor_completed", {
      monitor: "expired_orders",
      status: "completed",
    });
  }

  logMonitorFailed(data: MonitorErrorDTO): void {
    this.error("Expired orders monitor failed", data.error, "monitor_failed", {
      monitor: "expired_orders",
      status: data.status,
    });
  }
}
