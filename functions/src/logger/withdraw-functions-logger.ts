import { BaseLogger } from "./base-logger";

export interface WithdrawalProcessedDTO {
  userId: string;
  netAmountToUser: number;
  feeAmount: number;
  walletAddress: string;
}

export interface WithdrawalErrorDTO {
  error: unknown;
  userId?: string;
  requestData: any;
}

export class WithdrawFunctionsLogger extends BaseLogger {
  constructor() {
    super("withdraw-functions");
  }

  logWithdrawalProcessed(data: WithdrawalProcessedDTO): void {
    this.info(
      `Withdrawal processed: ${data.netAmountToUser} TON sent to ${data.walletAddress} (${data.feeAmount} TON fee applied)`,
      "withdrawal",
      {
        userId: data.userId,
        netAmountToUser: data.netAmountToUser,
        feeAmount: data.feeAmount,
        walletAddress: data.walletAddress,
      }
    );
  }

  logWithdrawalError(data: WithdrawalErrorDTO): void {
    this.error("Error processing withdrawal", data.error, "withdrawal", {
      userId: data.userId,
      requestData: data.requestData,
    });
  }
}
