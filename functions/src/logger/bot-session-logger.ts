import { BaseLogger, LogContext } from "./logger";

export class BotSessionLogger extends BaseLogger {
  constructor() {
    super("bot-session");
  }

  logSessionSaved(userId: string, sessionData: any): void {
    this.info("Bot session saved successfully", "save_bot_session", {
      userId,
      sessionData,
    });
  }

  logSessionSaveError(error: Error | any, userId: string): void {
    this.error("Error saving bot session", error, "save_bot_session", {
      userId,
    });
  }

  logSessionRetrieved(userId: string): void {
    this.info("Bot session retrieved successfully", "get_bot_session", {
      userId,
    });
  }

  logSessionNotFound(userId: string): void {
    this.info("Bot session not found", "get_bot_session", {
      userId,
    });
  }

  logSessionRetrieveError(error: Error | any, userId: string): void {
    this.error("Error retrieving bot session", error, "get_bot_session", {
      userId,
    });
  }

  logSessionCleared(userId: string): void {
    this.info("Bot session cleared successfully", "clear_bot_session", {
      userId,
    });
  }

  logSessionClearError(error: Error | any, userId: string): void {
    this.error("Error clearing bot session", error, "clear_bot_session", {
      userId,
    });
  }
}
