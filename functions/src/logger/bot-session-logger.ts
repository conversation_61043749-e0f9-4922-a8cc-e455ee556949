import { BaseLogger } from "./base-logger";

export interface SessionSavedDTO {
  userId: string;
  sessionData: any;
}

export interface SessionErrorDTO {
  error: unknown;
  userId: string;
}

export interface SessionUserDTO {
  userId: string;
}

export class BotSessionLogger extends BaseLogger {
  constructor() {
    super("bot-session");
  }

  logSessionSaved(data: SessionSavedDTO): void {
    this.info("Bot session saved successfully", "save_bot_session", {
      userId: data.userId,
      sessionData: data.sessionData,
    });
  }

  logSessionSaveError(data: SessionErrorDTO): void {
    this.error("Error saving bot session", data.error, "save_bot_session", {
      userId: data.userId,
    });
  }

  logSessionRetrieved(data: SessionUserDTO): void {
    this.info("Bot session retrieved successfully", "get_bot_session", {
      userId: data.userId,
    });
  }

  logSessionNotFound(data: SessionUserDTO): void {
    this.info("Bot session not found", "get_bot_session", {
      userId: data.userId,
    });
  }

  logSessionRetrieveError(data: SessionErrorDTO): void {
    this.error("Error retrieving bot session", data.error, "get_bot_session", {
      userId: data.userId,
    });
  }

  logSessionCleared(data: SessionUserDTO): void {
    this.info("Bot session cleared successfully", "clear_bot_session", {
      userId: data.userId,
    });
  }

  logSessionClearError(data: SessionErrorDTO): void {
    this.error("Error clearing bot session", data.error, "clear_bot_session", {
      userId: data.userId,
    });
  }
}
