import { BaseLogger } from "./base-logger";

export interface BotHealthCheckStartDTO {
  status: string;
}

export interface BotHealthCheckCallDTO {
  healthCheckUrl: string;
  hasAuthentication: boolean;
}

export interface BotHealthCheckResponseDTO {
  responseData: any;
}

export interface BotHealthCheckStatusDTO {
  status: string;
  responseData?: any;
}

export interface BotHealthCheckErrorDTO {
  error: unknown;
  status: string;
}

export interface BotHealthMonitorDTO {
  status: string;
  timestamp: string;
}

export class BotHealthCheckLogger extends BaseLogger {
  constructor() {
    super("bot-health-check");
  }

  logHealthCheckStarted(data: BotHealthCheckStartDTO): void {
    this.info("Starting bot health check", "health_check_start", {
      monitor: "bot_health",
      status: data.status,
    });
  }

  logHealthCheckCall(data: BotHealthCheckCallDTO): void {
    this.info(
      "Calling bot health check endpoint with authentication",
      "health_check_call",
      {
        monitor: "bot_health",
        healthCheckUrl: data.healthCheckUrl,
        hasAuthentication: data.hasAuthentication,
      }
    );
  }

  logHealthCheckResponse(data: BotHealthCheckResponseDTO): void {
    this.info("Bot health check response received", "health_check_response", {
      monitor: "bot_health",
      responseData: data.responseData,
    });
  }

  logHealthCheckPassed(data: BotHealthCheckStatusDTO): void {
    this.info(
      "✅ Bot health check passed - bot is healthy",
      "health_check_passed",
      {
        monitor: "bot_health",
        status: data.status,
      }
    );
  }

  logHealthCheckUnhealthy(data: BotHealthCheckStatusDTO): void {
    this.warn(
      "⚠️ Bot health check returned unhealthy status",
      "health_check_unhealthy",
      {
        monitor: "bot_health",
        status: data.status,
        responseData: data.responseData,
      }
    );
  }

  logHealthCheckFailed(data: BotHealthCheckErrorDTO): void {
    this.error(
      "❌ Bot health check failed",
      data.error,
      "health_check_failed",
      {
        monitor: "bot_health",
        status: data.status,
      }
    );
  }

  logMonitorTriggered(data: BotHealthMonitorDTO): void {
    this.info("Bot health check monitor triggered", "monitor_triggered", {
      monitor: "bot_health",
      status: data.status,
      timestamp: data.timestamp,
    });
  }

  logMonitorCompleted(): void {
    this.info(
      "Bot health check monitor completed successfully",
      "monitor_completed",
      {
        monitor: "bot_health",
        status: "completed",
      }
    );
  }

  logMonitorFailed(data: BotHealthCheckErrorDTO): void {
    this.error(
      "Bot health check monitor failed",
      data.error,
      "monitor_failed",
      {
        monitor: "bot_health",
        status: data.status,
      }
    );
  }
}
