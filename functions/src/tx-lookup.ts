import * as admin from "firebase-admin";
import { TxLookupEntity } from "@mikerudenko/marketplace-shared";
import { log } from "./logger/logger";

const TX_LOOKUP_COLLECTION = "tx_lookup";
const TX_LOOKUP_DOC_ID = "main";

export async function getTxLookup() {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(TX_LOOKUP_COLLECTION)
      .doc(TX_LOOKUP_DOC_ID)
      .get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as TxLookupEntity;
  } catch (error) {
    log.error("Error getting tx lookup", error, {
      operation: "get_tx_lookup",
    });
    throw error;
  }
}

export async function updateTxLookup(lastCheckedRecordId: string) {
  try {
    const db = admin.firestore();
    await db.collection(TX_LOOKUP_COLLECTION).doc(TX_LOOKUP_DOC_ID).set(
      {
        last_checked_record_id: lastCheckedRecordId,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      { merge: true }
    );

    log.info(
      `Updated tx lookup with last_checked_record_id: ${lastCheckedRecordId}`,
      {
        operation: "update_tx_lookup",
        lastCheckedRecordId,
      }
    );
  } catch (error) {
    log.error("Error updating tx lookup", error, {
      operation: "update_tx_lookup",
      lastCheckedRecordId,
    });
    throw error;
  }
}
