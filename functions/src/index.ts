import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";

import { getEnv } from "./config";
import { monitorTonTransactions } from "./ton-monitor";
import { log } from "./logger/logger";
import { commonFunctionsConfig } from "./constants";

if (!admin.apps.length) {
  const config = getEnv();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    log.info("Firebase initialized with service account key", {
      operation: "firebase_init",
      method: "service_account",
    });
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    log.info("Firebase initialized with application default credentials", {
      operation: "firebase_init",
      method: "application_default",
    });
  }
}

admin.firestore().settings({
  ignoreUndefinedProperties: true,
});

export const tonTransactionMonitor = onSchedule(
  {
    schedule: "* * * * *",
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },

  async () => {
    try {
      log.monitorLog("TON transaction monitor triggered", {
        monitor: "ton_transaction",
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await monitorTonTransactions();
      log.monitorLog("TON transaction monitor completed successfully", {
        monitor: "ton_transaction",
        status: "completed",
      });
    } catch (error) {
      log.error("TON transaction monitor failed", error, {
        monitor: "ton_transaction",
        status: "failed",
      });
    }
  }
);

export { expiredOrdersMonitor } from "./expired-orders-monitor";

export { limitedCollectionsMonitor } from "./limited-collections-monitor";

export { botHealthCheck } from "./bot-health-check";

export {
  completePurchaseByBot,
  getOrderByIdByBot,
  getUserOrdersByBot,
  sendGiftToRelayerByBot,
} from "./order-functions/bot-order-functions";

export {
  createOrderAsBuyer,
  makePurchaseAsBuyer,
} from "./order-functions/buyer-order-functions";

export {
  createOrderAsSeller,
  makePurchaseAsSeller,
} from "./order-functions/seller-order-functions";

export { cancelUserOrder } from "./order-functions/general-order-functions";

export { fulfillOrderAndCreateResellOrder } from "./order-functions/fulfill-and-resell-functions";

export { withdrawFunds } from "./withdraw-functions";

export { withdrawRevenue } from "./revenue-functions";

export { signInWithTelegram } from "./telegram-auth-functions";

export { changeUserData } from "./user-profile-functions";

export {
  makeSecondaryMarketPurchase,
  setSecondaryMarketPrice,
} from "./order-functions/secondary-market-functions";

export {
  clearUserSessionByBot,
  getUserSessionByBot,
  saveUserSessionByBot,
} from "./bot-session-functions";

export {
  clearOrderDeadlines,
  recalculateOrderDeadlines,
} from "./admin-collection-functions";
