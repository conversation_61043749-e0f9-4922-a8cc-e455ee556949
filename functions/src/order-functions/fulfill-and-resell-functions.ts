import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../constants";
import {
  FULFILL_AND_RESELL_ERRORS,
  GENERIC_ERRORS,
} from "../constants/error-messages";
import { requireAuthentication } from "../services/auth-middleware";
import { validateSufficientFunds } from "../services/balance-service";
import { createOrder } from "../services/order-creation-service";
import { log } from "../logger/logger";
import { getAppConfig } from "../services/fee-service";
import {
  OrderEntity,
  OrderStatus,
  UserType,
} from "@mikerudenko/marketplace-shared";

export const fulfillOrderAndCreateResellOrder = onCall<{
  orderId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { orderId, price } = request.data;

  if (!orderId || !price || price <= 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: FULFILL_AND_RESELL_ERRORS.INVALID_PARAMETERS,
        fallbackMessage:
          "Order ID and resell price are required and must be valid",
      })
    );
  }

  try {
    const db = admin.firestore();
    const userId = authRequest.auth.uid;

    // Get the order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError(
        "not-found",
        JSON.stringify({
          errorKey: FULFILL_AND_RESELL_ERRORS.ORDER_NOT_FOUND,
          fallbackMessage: "Order not found",
        })
      );
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Validate that the current user is the buyer of this order
    if (order.buyerId !== userId) {
      throw new HttpsError(
        "permission-denied",
        JSON.stringify({
          errorKey: FULFILL_AND_RESELL_ERRORS.NOT_ORDER_BUYER,
          fallbackMessage: "You are not the buyer of this order",
        })
      );
    }

    // Validate that the order status is "gift_sent_to_relayer"
    if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
      throw new HttpsError(
        "failed-precondition",
        JSON.stringify({
          errorKey: FULFILL_AND_RESELL_ERRORS.INVALID_ORDER_STATUS,
          fallbackMessage:
            "Order must have status 'gift_sent_to_relayer' to be resold",
        })
      );
    }

    const appConfig = await getAppConfig();

    // Validate that the user has sufficient balance to create the resell order
    const sellerLockPercentageBPS = appConfig.seller_lock_percentage;
    const sellerLockPercentage = sellerLockPercentageBPS / 10000;
    const lockAmount = price * sellerLockPercentage;

    await validateSufficientFunds({
      userId,
      amount: lockAmount,
      operation: "fulfill and resell order creation",
    });

    // Step 1: Update the original order status to fulfilled
    await db.collection("orders").doc(orderId).update({
      status: OrderStatus.FULFILLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Step 2: Create a new order where the current user is the seller
    const newOrderResult = await createOrder(db, {
      userId,
      collectionId: order.collectionId,
      price,
      gift: order.gift ?? null,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });

    log.info("Order fulfilled and resell order created", {
      originalOrderId: orderId,
      newOrderId: newOrderResult.orderId,
      userId,
      price,
      operation: "fulfill_and_resell",
    });

    return {
      success: true,
      message: "Order fulfilled and resell order created successfully",
      originalOrderId: orderId,
      newOrderId: newOrderResult.orderId,
      price,
      lockAmount,
    };
  } catch (error) {
    log.error("Error fulfilling order and creating resell order", error, {
      operation: "fulfill_and_resell",
      requestData: request.data,
      userId: request.auth?.uid,
    });

    // Re-throw HttpsError as-is
    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ??
          "Server error while fulfilling and reselling order.",
      })
    );
  }
});
