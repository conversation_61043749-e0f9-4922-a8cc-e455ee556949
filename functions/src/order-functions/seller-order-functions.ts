import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validatePurchaseParams,
  validateSellerOwnership,
} from "../services/auth-middleware";
import { createOrder } from "../services/order-creation-service";
import { processPurchase } from "../services/purchase-flow-service";
import { log } from "../logger/logger";
import { GENERIC_ERRORS } from "../constants/error-messages";
import { commonFunctionsConfig } from "../constants";
import { UserType } from "@mikerudenko/marketplace-shared";

export const createOrderAsSeller = onCall<{
  sellerId: string;
  collectionId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { sellerId, collectionId, price } = request.data;

  validateOrderCreationParams(request.data, UserType.SELLER);
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    const result = await createOrder(db, {
      userId: sellerId,
      collectionId,
      price,
      gift: null,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });

    return result;
  } catch (error) {
    log.error("Error creating order as seller", error, {
      sellerId,
      collectionId,
      price,
      operation: "seller_order_creation",
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while creating order.",
      })
    );
  }
});

export const makePurchaseAsSeller = onCall<{
  sellerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { sellerId, orderId } = request.data;

  validatePurchaseParams(request.data, UserType.SELLER);
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    const result = await processPurchase(db, {
      userId: sellerId,
      orderId,
      userType: UserType.SELLER,
    });

    return result;
  } catch (error) {
    log.error("Error making purchase as seller", error, {
      operation: "purchase_as_seller",
      requestData: request.data,
      userId: request.auth?.uid,
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while making purchase.",
      })
    );
  }
});
