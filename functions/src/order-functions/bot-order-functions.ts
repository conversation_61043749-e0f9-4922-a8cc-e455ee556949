import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  GENERIC_ERRORS,
  ORDER_ERRORS,
  VALIDATION_ERRORS,
} from "../constants/error-messages";
import {
  addFundsWithHistory,
  spendLockedFunds,
} from "../services/balance-service";
import { verifyBotToken } from "../services/bot-auth-service";
import { safeMultiply } from "../utils";
import { BotOrderLogger } from "../logger";
import { commonFunctionsConfig } from "../constants";
import {
  OrderEntity,
  OrderGift,
  OrderStatus,
  TxType,
  UserEntity,
  formatDateToFirebaseTimestamp,
} from "@mikerudenko/marketplace-shared";

const botOrderLogger = new BotOrderLogger();

export const getOrderByIdByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  if (!orderId) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_ORDER_ID,
        fallbackMessage: "Order ID is required.",
      })
    );
  }

  if (!botToken) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
        fallbackMessage: "Bot token is required.",
      })
    );
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
        fallbackMessage: "Invalid bot token.",
      })
    );
  }

  try {
    const db = admin.firestore();
    const orderDoc = await db.collection("orders").doc(orderId).get();

    if (!orderDoc.exists) {
      return {
        success: false,
        order: null,
        message: "Order not found.",
      };
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    let buyer_tg_id: string | undefined;
    let seller_tg_id: string | undefined;

    if (order.buyerId) {
      const buyerDoc = await db.collection("users").doc(order.buyerId).get();
      if (buyerDoc.exists) {
        const buyerData = buyerDoc.data() as UserEntity;
        buyer_tg_id = buyerData.tg_id;
      }
    }

    if (order.sellerId) {
      const sellerDoc = await db.collection("users").doc(order.sellerId).get();
      if (sellerDoc.exists) {
        const sellerData = sellerDoc.data() as UserEntity;
        seller_tg_id = sellerData.tg_id;
      }
    }

    return {
      success: true,
      order: {
        ...order,
        buyer_tg_id,
        seller_tg_id,
      },
      message: "Order retrieved successfully.",
    };
  } catch (error) {
    botOrderLogger.logGetOrderError({
      error,
      orderId: request.data.orderId,
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while getting order.",
      })
    );
  }
});

export const getUserOrdersByBot = onCall<{
  userId?: string;
  tgId?: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, tgId, botToken } = request.data;

  if (!botToken) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
        fallbackMessage: "Bot token is required.",
      })
    );
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
        fallbackMessage: "Invalid bot token.",
      })
    );
  }

  try {
    const db = admin.firestore();
    let targetUserId = userId;

    if (!targetUserId && tgId) {
      const usersQuery = await db
        .collection("users")
        .where("tg_id", "==", tgId)
        .limit(1)
        .get();

      if (usersQuery.empty) {
        return {
          success: false,
          orders: [],
          sellOrders: [],
          buyOrders: [],
          count: 0,
          sellOrdersCount: 0,
          buyOrdersCount: 0,
          userId: "",
          message: "User not found with the provided Telegram ID.",
        };
      }

      targetUserId = usersQuery.docs[0].id;
    }

    if (!targetUserId) {
      throw new HttpsError(
        "invalid-argument",
        JSON.stringify({
          errorKey: VALIDATION_ERRORS.USER_ID_OR_TG_ID_REQUIRED,
          fallbackMessage: "Either userId or tgId is required.",
        })
      );
    }

    const sellOrdersQuery = await db
      .collection("orders")
      .where("sellerId", "==", targetUserId)
      .where("status", "==", OrderStatus.PAID)
      .get();

    const buyOrdersQuery = await db
      .collection("orders")
      .where("buyerId", "==", targetUserId)
      .where("status", "==", OrderStatus.GIFT_SENT_TO_RELAYER)
      .get();

    const sellOrders: OrderEntity[] = [];
    const buyOrders: OrderEntity[] = [];

    sellOrdersQuery.forEach((doc) => {
      sellOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    buyOrdersQuery.forEach((doc) => {
      buyOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    const sortByDate = (a: OrderEntity, b: OrderEntity) => {
      const aTime = formatDateToFirebaseTimestamp(a.createdAt)?.toMillis() || 0;
      const bTime = formatDateToFirebaseTimestamp(b.createdAt)?.toMillis() || 0;
      return bTime - aTime;
    };

    sellOrders.sort(sortByDate);
    buyOrders.sort(sortByDate);

    const allOrders = [...sellOrders, ...buyOrders];
    allOrders.sort(sortByDate);

    return {
      success: true,
      orders: allOrders,
      sellOrders,
      buyOrders,
      count: allOrders.length,
      sellOrdersCount: sellOrders.length,
      buyOrdersCount: buyOrders.length,
      userId: targetUserId,
    };
  } catch (error) {
    botOrderLogger.logGetUserOrdersError({
      error,
      userId: request.data.userId,
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while getting user orders.",
      })
    );
  }
});

export const sendGiftToRelayerByBot = onCall<{
  orderId: string;
  botToken: string;
  gift: OrderGift;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken, gift } = request.data;

  if (!gift) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.GIFT_REQUIRED,
        fallbackMessage: "Owned gift is required.",
      })
    );
  }

  if (!orderId) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_ORDER_ID,
        fallbackMessage: "Order ID is required.",
      })
    );
  }

  if (!botToken) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
        fallbackMessage: "Bot token is required.",
      })
    );
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
        fallbackMessage: "Invalid bot token.",
      })
    );
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError(
        "not-found",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
          fallbackMessage: "Order not found.",
        })
      );
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    if (order.status !== OrderStatus.PAID) {
      throw new HttpsError(
        "failed-precondition",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_MUST_BE_PAID_STATUS,
          fallbackMessage:
            "Order must be in 'paid' status to send gift to relayer.",
        })
      );
    }

    if (!order.buyerId || !order.sellerId) {
      throw new HttpsError(
        "failed-precondition",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_MUST_HAVE_BUYER_AND_SELLER,
          fallbackMessage: "Order must have both buyer and seller.",
        })
      );
    }

    // Process financial transactions when gift is sent to relayer
    // Fees have already been applied during purchase, so we calculate net amount to seller

    // Use fees from order object to calculate locked amounts and fees
    const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
    const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
    const purchaseFeeBPS = order.fees?.purchase_fee ?? 0;

    const buyerLockPercentage = buyerLockPercentageBPS / 10000; // Convert BPS to decimal
    const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

    const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
    const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

    // Calculate the fees that were already applied during purchase
    const totalFeeAmount =
      purchaseFeeBPS > 0
        ? safeMultiply(order.price, purchaseFeeBPS / 10000)
        : 0;

    // Net amount to seller is order price minus fees that were already applied
    const netAmountToSeller = order.price - totalFeeAmount;

    // Add reseller earnings for seller if any
    const resellerEarnings = order.reseller_earnings_for_seller ?? 0;
    const totalAmountToSeller = netAmountToSeller + resellerEarnings;

    await Promise.all([
      spendLockedFunds(order.buyerId, buyerLockedAmount),
      spendLockedFunds(order.sellerId, sellerLockedAmount),
    ]);

    await addFundsWithHistory({
      userId: order.sellerId,
      amount: netAmountToSeller + sellerLockedAmount,
      txType: TxType.SELL_FULFILLMENT,
      orderId: order.id,
      description: `Order fulfillment payment (${netAmountToSeller} TON net amount)`,
    });

    if (resellerEarnings > 0) {
      await addFundsWithHistory({
        userId: order.sellerId,
        amount: resellerEarnings,
        txType: TxType.RESELL_FEE_EARNINGS,
        orderId: order.id,
        description: `Resell fee earnings (${resellerEarnings} TON)`,
      });
    }

    await db.collection("orders").doc(orderId).update({
      status: OrderStatus.GIFT_SENT_TO_RELAYER,
      gift,
      secondaryMarketPrice: null,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message:
        "Gift sent to relayer successfully. Net amount transferred to seller.",
      netAmountToSeller,
      resellerEarnings,
      totalAmountToSeller,
      feesAlreadyApplied: totalFeeAmount,
      order: {
        id: order.id,
        number: order.number,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  } catch (error) {
    botOrderLogger.logSendGiftToRelayerError({
      error,
      orderId,
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ??
          "Server error while sending gift to relayer.",
      })
    );
  }
});

export const completePurchaseByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  if (!orderId) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_ORDER_ID,
        fallbackMessage: "Order ID is required.",
      })
    );
  }

  if (!botToken) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
        fallbackMessage: "Bot token is required.",
      })
    );
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
        fallbackMessage: "Invalid bot token.",
      })
    );
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError(
        "not-found",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
          fallbackMessage: "Order not found.",
        })
      );
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    if (order.status !== "gift_sent_to_relayer") {
      throw new HttpsError(
        "failed-precondition",
        JSON.stringify({
          errorKey: ORDER_ERRORS.ORDER_MUST_BE_GIFT_SENT_STATUS,
          fallbackMessage:
            "Order must be in 'gift_sent_to_relayer' status to complete purchase.",
        })
      );
    }

    await db.collection("orders").doc(orderId).update({
      status: "fulfilled",
      secondaryMarketPrice: null,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Purchase completed successfully.",
      order: {
        id: order.id,
        number: order.number,
        status: "fulfilled",
      },
    };
  } catch (error) {
    botOrderLogger.logCompletePurchaseError({
      error,
      orderId,
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while completing purchase.",
      })
    );
  }
});
