import * as admin from "firebase-admin";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import {
  processOrderCancellation,
  validateCancellationPermission,
} from "../services/order-cancellation-service";
import { requireAuthentication } from "../services/auth-middleware";
import { log } from "../logger/logger";
import { commonFunctionsConfig } from "../constants";
import { OrderEntity, OrderStatus } from "@mikerudenko/marketplace-shared";

export const cancelUserOrder = onCall<{
  orderId: string;
  userId: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, userId } = request.data;
  log.debug("Cancel order request received", {
    operation: "cancel_order",
    orderId,
    userId,
    environment: process.env.NODE_ENV,
  });

  const authRequest = requireAuthentication(request);

  if (!orderId || !userId) {
    throw new HttpsError(
      "invalid-argument",
      "orderId and userId are required."
    );
  }

  if (authRequest?.auth?.uid !== userId) {
    throw new HttpsError(
      "permission-denied",
      "You can only cancel your own orders."
    );
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Validate cancellation permission using the new service
    await validateCancellationPermission(order, userId);

    // Process the cancellation using unified logic
    const result = await processOrderCancellation(order, userId);

    return {
      success: result.success,
      message: result.message,
      order: {
        id: order.id,
        number: order.number,
        status: OrderStatus.CANCELLED,
      },
      feeApplied: result.feeApplied,
      feeType: result.feeType,
    };
  } catch (error) {
    log.error("Error cancelling order", error, {
      orderId,
      userId,
      operation: "cancel_user_order",
    });

    if (error instanceof Error) {
      throw new HttpsError("failed-precondition", error.message);
    }

    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while cancelling order."
    );
  }
});
