import { onSchedule } from "firebase-functions/v2/scheduler";
import fetch from "node-fetch";
import { BOT_HEALTH_CHECK_ENDPOINT, commonFunctionsConfig } from "./constants";
import { BotHealthCheckLogger } from "./logger";
import { getEnv } from "./config";

const botHealthLogger = new BotHealthCheckLogger();

export async function checkBotHealth() {
  try {
    botHealthLogger.logHealthCheckStarted({ status: "started" });

    const botAppUrl = getEnv().url.webhook_url;
    const bearerToken = process.env.HEALTH_CHECK_BEARER_TOKEN;

    if (!botAppUrl) {
      throw new Error("BOT_APP_URL environment variable is not configured");
    }

    if (!bearerToken) {
      throw new Error(
        "HEALTH_CHECK_BEARER_TOKEN environment variable is not configured"
      );
    }

    const healthCheckUrl = `${botAppUrl}${BOT_HEALTH_CHECK_ENDPOINT}`;
    botHealthLogger.logHealthCheckCall({
      healthCheckUrl,
      hasAuthentication: true,
    });

    const response = await fetch(healthCheckUrl, {
      method: "GET",
      timeout: 30000, // 30 second timeout
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        "Content-Type": "application/json",
        "User-Agent": "marketplace-functions/health-check",
      },
    });

    if (!response.ok) {
      throw new Error(
        `Bot health check failed with status ${response.status}: ${response.statusText}`
      );
    }

    const responseData = await response.json();
    botHealthLogger.logHealthCheckResponse({ responseData });

    if (responseData.status === "healthy") {
      botHealthLogger.logHealthCheckPassed({ status: "healthy" });
    } else {
      botHealthLogger.logHealthCheckUnhealthy({
        status: "unhealthy",
        responseData,
      });
    }

    return {
      success: true,
      status: responseData.status,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    botHealthLogger.logHealthCheckFailed({
      error,
      status: "failed",
    });
    throw error;
  }
}

export const botHealthCheck = onSchedule(
  {
    schedule: "*/15 * * * *", // Every 15 minutes
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      botHealthLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await checkBotHealth();
      botHealthLogger.logMonitorCompleted();
    } catch (error) {
      botHealthLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
