import { HttpsError } from "firebase-functions/v2/https";
import { verifyBotToken } from "./bot-auth-service";
import { VALIDATION_ERRORS } from "../constants/error-messages";

export interface BotValidationParams {
  userId?: string;
  botToken: string;
}

export interface BotValidationWithOrderParams {
  orderId?: string;
  botToken: string;
}

export function validateBotRequest(params: BotValidationParams): void {
  const { userId, botToken } = params;

  if (userId !== undefined && !userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }
}

export function validateBotRequestWithErrorKeys(
  params: BotValidationParams
): void {
  const { userId, botToken } = params;

  if (userId !== undefined && !userId) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_USER_ID,
        fallbackMessage: "User ID is required.",
      })
    );
  }

  if (!botToken) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
        fallbackMessage: "Bot token is required.",
      })
    );
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
        fallbackMessage: "Invalid bot token.",
      })
    );
  }
}

export function validateBotOrderRequest(
  params: BotValidationWithOrderParams
): void {
  const { orderId, botToken } = params;

  if (orderId !== undefined && !orderId) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_ORDER_ID,
        fallbackMessage: "Order ID is required.",
      })
    );
  }

  if (!botToken) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
        fallbackMessage: "Bot token is required.",
      })
    );
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
        fallbackMessage: "Invalid bot token.",
      })
    );
  }
}
