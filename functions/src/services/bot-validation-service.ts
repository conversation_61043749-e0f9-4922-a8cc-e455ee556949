import { HttpsError } from "firebase-functions/v2/https";
import { verifyBotToken } from "./bot-auth-service";

export interface BotValidationParams {
  userId?: string;
  botToken: string;
}

export function validateBotRequest(params: BotValidationParams): void {
  const { userId, botToken } = params;

  if (userId !== undefined && !userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }
}
