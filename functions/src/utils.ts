import { log } from "./logger/logger";

export function roundToThreeDecimals(value: number) {
  return Math.round(value * 1000) / 1000;
}

export function safeMultiply(amount: number, multiplier: number) {
  return roundToThreeDecimals(amount * multiplier);
}

export function safeDivide(amount: number, divisor: number) {
  if (divisor === 0) {
    log.error("Division by zero error in safeDivide function", undefined, {
      operation: "safe_divide",
      amount,
      divisor,
    });
    throw new Error("Division by zero");
  }
  return roundToThreeDecimals(amount / divisor);
}

export function safeAdd(a: number, b: number) {
  return roundToThreeDecimals(a + b);
}

export function safeSubtract(a: number, b: number) {
  return roundToThreeDecimals(a - b);
}

export function generateUniqueId() {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function extractRawTonAddress(address: string) {
  if (!address || address.length < 48) {
    return null;
  }

  return address.substring(2, address.length - 3);
}

export function isValidTonAddress(address: string) {
  if (!address || typeof address !== "string") {
    return false;
  }

  if (address.length !== 48) {
    return false;
  }

  if (!address.startsWith("EQ") && !address.startsWith("UQ")) {
    return false;
  }

  const addressPart = address.substring(2);
  const base64Regex = /^[A-Za-z0-9+/=_-]+$/;
  return base64Regex.test(addressPart);
}

export function prepareUserDataForSave(userData: any) {
  const result = { ...userData };

  if (userData.ton_wallet_address) {
    const rawAddress = extractRawTonAddress(userData.ton_wallet_address);
    if (rawAddress) {
      result.raw_ton_wallet_address = rawAddress;
    }
  }

  return result;
}
