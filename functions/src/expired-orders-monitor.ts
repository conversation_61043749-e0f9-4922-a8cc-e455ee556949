import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { processOrderCancellation } from "./services/order-cancellation-service";
import { ExpiredOrdersMonitorLogger } from "./logger";
import { commonFunctionsConfig } from "./constants";
import { OrderEntity, OrderStatus } from "@mikerudenko/marketplace-shared";

const db = admin.firestore();
const expiredOrdersLogger = new ExpiredOrdersMonitorLogger();

export async function processExpiredOrders() {
  try {
    expiredOrdersLogger.logProcessingStarted();

    const now = admin.firestore.Timestamp.now();

    // Only cancel orders where sellers failed to send gifts to relayer (status='paid')
    // Orders with status='gift_sent_to_relayer' are not cancelled - buyers can take gifts after deadline
    const expiredOrdersQuery = db
      .collection("orders")
      .where("status", "==", OrderStatus.PAID)
      .where("deadline", "<", now);

    const expiredOrdersSnapshot = await expiredOrdersQuery.get();

    if (expiredOrdersSnapshot.empty) {
      expiredOrdersLogger.logNoOrdersFound();
      return;
    }

    expiredOrdersLogger.logOrdersFound({ count: expiredOrdersSnapshot.size });

    for (const orderDoc of expiredOrdersSnapshot.docs) {
      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        expiredOrdersLogger.logOrderSkipped({
          orderId: order.id || "unknown",
          reason: "missing_buyer_or_seller",
        });
        continue;
      }

      try {
        const result = await processOrderCancellation(order, order.sellerId);
        expiredOrdersLogger.logOrderProcessed({
          orderId: order.id || "unknown",
          status: "processed",
          message: result.message,
        });
      } catch (error) {
        expiredOrdersLogger.logOrderProcessError({
          error,
          orderId: order.id || "unknown",
        });
      }
    }

    expiredOrdersLogger.logProcessingCompleted();
  } catch (error) {
    expiredOrdersLogger.logProcessingError({ error });
    throw error;
  }
}

export const expiredOrdersMonitor = onSchedule(
  {
    schedule: "0 0 * * *", // Run daily at midnight UTC
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      expiredOrdersLogger.logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await processExpiredOrders();
      expiredOrdersLogger.logMonitorCompleted();
    } catch (error) {
      expiredOrdersLogger.logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
